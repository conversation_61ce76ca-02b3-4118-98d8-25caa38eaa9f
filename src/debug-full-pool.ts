#!/usr/bin/env node

import { SolendClient } from './solend-client';
import { Connection, PublicKey } from '@solana/web3.js';
import { config } from './config';
import { SolendMarket } from '@solendprotocol/solend-sdk';

async function debugFullPool() {
  console.log('🔍 Debugging full pool (USDC + TRUNK)...\n');
  
  try {
    // Initialize client
    const client = new SolendClient();
    await client.initialize();
    
    // Get market directly for detailed analysis
    const connection = new Connection(config.rpcEndpoint, 'confirmed');
    const market = await SolendMarket.initialize(
      connection,
      'production',
      config.poolAddress
    );
    
    await market.loadReserves();
    await market.refreshAll();
    
    // Get user obligation
    const obligation = await market.fetchObligationByWallet(
      new PublicKey(config.walletAddress)
    );
    
    console.log('=== FULL POOL ANALYSIS ===');
    
    // Analyze both reserves
    const usdcMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
    const trunkMint = '9mV4WUukVsva5wYcYW4veo34CNDiF44sh3Ji65JNdvh5';
    
    const usdcReserve = market.reserves.find(r => r.config.liquidityToken.mint === usdcMint);
    const trunkReserve = market.reserves.find(r => r.config.liquidityToken.mint === trunkMint);
    
    let totalPoolDepositsUSD = 0;
    let totalPoolBorrowsUSD = 0;
    let totalAvailableLiquidityUSD = 0;
    
    if (usdcReserve && usdcReserve.stats) {
      console.log('\n=== USDC RESERVE ===');
      const usdcDeposits = parseFloat(usdcReserve.stats.totalDepositsWads.toString()) / Math.pow(10, 24);
      const usdcBorrows = parseFloat(usdcReserve.stats.totalBorrowsWads.toString()) / Math.pow(10, 24);
      const usdcLiquidity = usdcDeposits - usdcBorrows;
      
      console.log(`USDC Deposits: ${usdcDeposits.toFixed(6)} USDC ($${usdcDeposits.toFixed(2)})`);
      console.log(`USDC Borrows: ${usdcBorrows.toFixed(6)} USDC ($${usdcBorrows.toFixed(2)})`);
      console.log(`USDC Available: ${usdcLiquidity.toFixed(6)} USDC ($${usdcLiquidity.toFixed(2)})`);
      console.log(`USDC Utilization: ${((usdcBorrows / usdcDeposits) * 100).toFixed(2)}%`);
      
      totalPoolDepositsUSD += usdcDeposits;
      totalPoolBorrowsUSD += usdcBorrows;
      totalAvailableLiquidityUSD += usdcLiquidity;
    }
    
    if (trunkReserve && trunkReserve.stats) {
      console.log('\n=== TRUNK RESERVE ===');
      const trunkDeposits = parseFloat(trunkReserve.stats.totalDepositsWads.toString()) / Math.pow(10, 24);
      const trunkBorrows = parseFloat(trunkReserve.stats.totalBorrowsWads.toString()) / Math.pow(10, 24);
      const trunkLiquidity = trunkDeposits - trunkBorrows;
      
      // TRUNK price assumption (very low value token)
      const trunkPrice = 0.001; // $0.001 per TRUNK
      
      console.log(`TRUNK Deposits: ${trunkDeposits.toFixed(6)} TRUNK ($${(trunkDeposits * trunkPrice).toFixed(2)})`);
      console.log(`TRUNK Borrows: ${trunkBorrows.toFixed(6)} TRUNK ($${(trunkBorrows * trunkPrice).toFixed(2)})`);
      console.log(`TRUNK Available: ${trunkLiquidity.toFixed(6)} TRUNK ($${(trunkLiquidity * trunkPrice).toFixed(2)})`);
      console.log(`TRUNK Utilization: ${((trunkBorrows / trunkDeposits) * 100).toFixed(2)}%`);
      
      totalPoolDepositsUSD += trunkDeposits * trunkPrice;
      totalPoolBorrowsUSD += trunkBorrows * trunkPrice;
      totalAvailableLiquidityUSD += trunkLiquidity * trunkPrice;
    }
    
    console.log('\n=== COMBINED POOL TOTALS ===');
    console.log(`Total Deposits: $${totalPoolDepositsUSD.toFixed(2)}`);
    console.log(`Total Borrows: $${totalPoolBorrowsUSD.toFixed(2)}`);
    console.log(`Total Available (TVL): $${totalAvailableLiquidityUSD.toFixed(2)}`);
    console.log(`Overall Utilization: ${((totalPoolBorrowsUSD / totalPoolDepositsUSD) * 100).toFixed(2)}%`);
    
    console.log('\n=== EXPECTED FROM SAVE.FINANCE ===');
    console.log('Total Deposits: $4,969,056.90');
    console.log('Total Borrows: $4,306,334.76');
    console.log('TVL: $662,722.13');
    console.log('USDC Deposits: 4,306,536.898161490045063063743026');
    console.log('TRUNK Deposits: 9,581,915.**************************');
    
    // Check user position with different calculation methods
    if (obligation && obligation.deposits.length > 0) {
      console.log('\n=== USER POSITION ANALYSIS ===');
      
      const usdcDeposit = obligation.deposits.find(d => d.mintAddress === usdcMint);
      if (usdcDeposit && usdcReserve && usdcReserve.stats) {
        const rawAmount = parseFloat(usdcDeposit.amount.toString());
        const exchangeRate = parseFloat(usdcReserve.stats.cTokenExchangeRate?.toString() || '1');
        
        console.log(`Raw deposit amount: ${rawAmount}`);
        console.log(`Exchange rate: ${exchangeRate}`);
        
        // Try different calculation methods
        const method1 = rawAmount / Math.pow(10, 6); // Direct division by token decimals
        const method2 = rawAmount / exchangeRate / Math.pow(10, 6); // With exchange rate
        const method3 = rawAmount * exchangeRate / Math.pow(10, 6); // Multiply by exchange rate
        const method4 = rawAmount / Math.pow(10, 9); // Different decimals
        
        console.log(`Method 1 (raw / 10^6): ${method1.toFixed(6)} USDC`);
        console.log(`Method 2 (raw / rate / 10^6): ${method2.toFixed(6)} USDC`);
        console.log(`Method 3 (raw * rate / 10^6): ${method3.toFixed(6)} USDC`);
        console.log(`Method 4 (raw / 10^9): ${method4.toFixed(6)} USDC`);
        
        console.log(`\n🎯 Expected: 8,321.496 USDC`);
        console.log(`🎯 Current: ${method2.toFixed(6)} USDC`);
        
        // Check if we need to use obligation stats instead
        if (obligation.obligationStats) {
          console.log(`\nObligation stats deposit: ${obligation.obligationStats.userTotalDeposit.toFixed(6)}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

if (require.main === module) {
  debugFullPool();
}

export { debugFullPool };
