#!/usr/bin/env node

import { SolendClient } from './solend-client';
import { notificationService } from './notifications';

async function testMonitor() {
  console.log('🧪 Testing monitor functionality...\n');
  
  try {
    // Test 1: Initialize client
    console.log('1️⃣ Testing Solend client initialization...');
    const client = new SolendClient();
    await client.initialize();
    console.log('   ✅ Solend client initialized successfully');
    
    // Test 2: Get pool status
    console.log('\n2️⃣ Testing pool status retrieval...');
    const status = await client.getPoolStatus();
    console.log('   ✅ Pool status retrieved successfully');
    console.log(`      User deposits: ${status.userDeposits.toFixed(6)} USDC`);
    console.log(`      Can withdraw: ${status.canWithdraw ? 'YES' : 'NO'}`);
    console.log(`      Max withdraw: ${status.maxWithdrawAmount.toFixed(6)} USDC`);
    console.log(`      Health factor: ${status.healthFactor.toFixed(2)}`);
    
    // Test 3: Wallet balance
    console.log('\n3️⃣ Testing wallet balance...');
    const balance = await client.getWalletBalance();
    console.log('   ✅ Wallet balance retrieved successfully');
    console.log(`      SOL: ${balance.sol.toFixed(6)}`);
    console.log(`      USDC: ${balance.usdc.toFixed(6)}`);
    
    // Test 4: Notification system
    console.log('\n4️⃣ Testing notification system...');
    await notificationService.sendNotification({
      title: 'Monitor Test',
      message: 'This is a test notification from the TRUNK-USDC monitor.',
      type: 'info',
      data: {
        userDeposits: status.userDeposits,
        canWithdraw: status.canWithdraw,
        maxWithdrawAmount: status.maxWithdrawAmount,
        healthFactor: status.healthFactor
      }
    });
    console.log('   ✅ Notification sent successfully');
    
    // Test 5: Withdrawal conditions
    console.log('\n5️⃣ Testing withdrawal conditions...');
    if (status.canWithdraw) {
      console.log('   ✅ Withdrawal conditions met');
      console.log(`      You can withdraw up to ${status.maxWithdrawAmount.toFixed(6)} USDC`);
      
      if (status.maxWithdrawAmount >= 1) {
        console.log('   ✅ Withdrawal amount above minimum threshold');
        console.log('   ⚠️  NOTE: Actual withdrawal not performed in test mode');
      } else {
        console.log('   ⚠️  Withdrawal amount below minimum threshold');
      }
    } else {
      console.log('   ❌ Withdrawal conditions not met');
      if (status.userBorrows > 0) {
        console.log('      Reason: Outstanding borrows');
      } else if (status.healthFactor < 1.5) {
        console.log('      Reason: Health factor too low');
      }
    }
    
    console.log('\n🎉 Monitor test completed successfully!');
    console.log('\nSummary:');
    console.log(`- Your USDC deposits: ${status.userDeposits.toFixed(6)}`);
    console.log(`- Withdrawal possible: ${status.canWithdraw ? 'YES' : 'NO'}`);
    console.log(`- Max withdrawal: ${status.maxWithdrawAmount.toFixed(6)} USDC`);
    console.log(`- Health factor: ${status.healthFactor.toFixed(2)}`);
    console.log(`- SOL balance: ${balance.sol.toFixed(6)}`);
    
    if (status.canWithdraw && status.maxWithdrawAmount >= 1) {
      console.log('\n🚀 Ready to start monitoring! Run "npm run dev monitor" to begin.');
    } else {
      console.log('\n⏳ Monitoring can be started, but withdrawal conditions are not currently met.');
    }
    
  } catch (error) {
    console.error('\n❌ Monitor test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  testMonitor();
}

export { testMonitor };
