#!/usr/bin/env node

import { SolendClient } from './solend-client';
import { SolendAction } from '@solendprotocol/solend-sdk';
import { Connection, PublicKey } from '@solana/web3.js';
import { config } from './config';
import BN from 'bn.js';
import bs58 from 'bs58';

async function testWithdrawalTransaction() {
  console.log('🧪 Testing withdrawal transaction building...\n');
  
  try {
    // Test 1: Initialize client
    console.log('1️⃣ Testing Solend client initialization...');
    const client = new SolendClient();
    await client.initialize();
    console.log('   ✅ Solend client initialized successfully');
    
    // Test 2: Get current status
    console.log('\n2️⃣ Getting current pool status...');
    const status = await client.getPoolStatus();
    console.log('   ✅ Pool status retrieved successfully');
    console.log(`      User deposits: ${status.userDeposits.toFixed(6)} USDC`);
    console.log(`      Available liquidity: ${status.availableLiquidity.toFixed(6)} USDC`);
    console.log(`      Max withdraw: ${status.maxWithdrawAmount.toFixed(6)} USDC`);
    
    // Test 3: Test transaction building (even with no liquidity)
    console.log('\n3️⃣ Testing withdrawal transaction building...');
    
    // Use a very small amount for testing (0.01 USDC)
    const testAmount = 0.01;
    console.log(`   Testing with ${testAmount} USDC withdrawal...`);
    
    try {
      // Create connection and wallet
      const connection = new Connection(config.rpcEndpoint, 'confirmed');
      const privateKeyBytes = bs58.decode(config.privateKey);
      const wallet = { publicKey: new PublicKey(config.walletAddress) };
      
      // Build withdrawal transaction using SolendAction
      const withdrawAmountLamports = new BN(testAmount * Math.pow(10, 6)); // USDC has 6 decimals
      
      console.log(`   Building transaction for ${withdrawAmountLamports.toString()} lamports...`);
      
      const solendAction = await SolendAction.buildWithdrawTxns(
        connection,
        withdrawAmountLamports,
        'USDC', // Use symbol
        wallet.publicKey,
        'production',
        new PublicKey(config.poolAddress) // Provide lending market address
      );
      
      console.log('   ✅ Transaction built successfully!');

      // Get transactions using the correct method
      const transactionData = await solendAction.getTransactions();
      console.log('   📄 Transaction structure:');
      console.log(`      Pre-lending transaction: ${transactionData.preLendingTxn ? 'YES' : 'NO'}`);
      console.log(`      Lending transaction: ${transactionData.lendingTxn ? 'YES' : 'NO'}`);
      console.log(`      Post-lending transaction: ${transactionData.postLendingTxn ? 'YES' : 'NO'}`);

      // Analyze the main lending transaction
      if (transactionData.lendingTxn) {
        const tx = transactionData.lendingTxn;
        console.log(`   📋 Instructions in lending transaction: ${tx.instructions.length}`);

        tx.instructions.forEach((instruction: any, i: number) => {
          console.log(`      Instruction ${i}: ${instruction.programId.toString()}`);
          console.log(`         Keys: ${instruction.keys.length}`);
        });
      }
      
      console.log('   ⚠️  NOTE: Transaction not sent (test mode)');
      
    } catch (transactionError) {
      console.log('   ❌ Transaction building failed:', transactionError);
      
      // This might be expected if there's no liquidity or other constraints
      if (transactionError instanceof Error) {
        if (transactionError.message.includes('insufficient liquidity') || 
            transactionError.message.includes('NotEnoughAccountKeys') ||
            transactionError.message.includes('Obligation has no deposits')) {
          console.log('   ℹ️  This error is expected when there\'s no liquidity or specific market constraints');
        } else {
          console.log('   ⚠️  Unexpected transaction error - this might indicate a real issue');
        }
      }
    }
    
    // Test 4: Test with actual withdrawal attempt (if liquidity exists)
    console.log('\n4️⃣ Testing actual withdrawal attempt...');
    
    if (status.maxWithdrawAmount > 0) {
      console.log('   💰 Liquidity available - testing actual withdrawal...');
      const attempt = await client.attemptWithdrawal(Math.min(0.01, status.maxWithdrawAmount));
      
      if (attempt.success) {
        console.log('   🎉 Withdrawal successful!');
        console.log(`   📄 Transaction ID: ${attempt.transactionId}`);
      } else {
        console.log('   ❌ Withdrawal failed:', attempt.error);
      }
    } else {
      console.log('   ⏳ No liquidity available - skipping actual withdrawal test');
      console.log('   ℹ️  This is expected when the pool has 100% utilization');
    }
    
    console.log('\n🎉 Withdrawal transaction test completed!');
    
    console.log('\nSummary:');
    console.log(`- Current liquidity: ${status.availableLiquidity.toFixed(6)} USDC`);
    console.log(`- Withdrawal possible: ${status.canWithdraw ? 'YES' : 'NO'}`);
    console.log(`- Max withdrawal: ${status.maxWithdrawAmount.toFixed(6)} USDC`);
    
  } catch (error) {
    console.error('\n❌ Withdrawal transaction test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  testWithdrawalTransaction();
}

export { testWithdrawalTransaction };
