#!/usr/bin/env node

import { SolendClient } from './solend-client';
import { Connection, PublicKey } from '@solana/web3.js';
import { config } from './config';
import { SolendMarket } from '@solendprotocol/solend-sdk';

async function debugPosition() {
  console.log('🔍 Debugging position calculation...\n');
  
  try {
    // Initialize client
    const client = new SolendClient();
    await client.initialize();
    
    // Get market directly for detailed analysis
    const connection = new Connection(config.rpcEndpoint, 'confirmed');
    const market = await SolendMarket.initialize(
      connection,
      'production',
      config.poolAddress
    );
    
    await market.loadReserves();
    await market.refreshAll();
    
    // Get user obligation
    const obligation = await market.fetchObligationByWallet(
      new PublicKey(config.walletAddress)
    );
    
    if (!obligation) {
      console.log('❌ No obligation found');
      return;
    }
    
    console.log('=== RAW OBLIGATION DATA ===');
    console.log(`Deposits count: ${obligation.deposits.length}`);
    console.log(`Borrows count: ${obligation.borrows.length}`);
    
    // Find USDC reserve
    const usdcMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
    const usdcReserve = market.reserves.find(r => r.config.liquidityToken.mint === usdcMint);
    
    if (!usdcReserve) {
      console.log('❌ USDC reserve not found');
      return;
    }
    
    console.log('\n=== USDC RESERVE INFO ===');
    console.log(`Reserve address: ${usdcReserve.config.address}`);
    console.log(`Token decimals: ${usdcReserve.stats?.decimals}`);
    console.log(`Exchange rate: ${usdcReserve.stats?.cTokenExchangeRate?.toString()}`);
    
    // Analyze each deposit
    obligation.deposits.forEach((deposit, i) => {
      console.log(`\n=== DEPOSIT ${i} ===`);
      console.log(`Mint: ${deposit.mintAddress}`);
      console.log(`Raw amount: ${deposit.amount.toString()}`);

      if (deposit.mintAddress === usdcMint) {
        console.log('\n🎯 THIS IS THE USDC DEPOSIT');

        // Try different conversion methods
        const rawAmount = parseFloat(deposit.amount.toString());
        
        console.log('\n--- CONVERSION ATTEMPTS ---');
        console.log(`Raw amount: ${rawAmount}`);
        console.log(`Raw / 10^6: ${(rawAmount / Math.pow(10, 6)).toFixed(6)} USDC`);
        console.log(`Raw / 10^8: ${(rawAmount / Math.pow(10, 8)).toFixed(6)} USDC`);
        console.log(`Raw / 10^9: ${(rawAmount / Math.pow(10, 9)).toFixed(6)} USDC`);
        console.log(`Raw / 10^12: ${(rawAmount / Math.pow(10, 12)).toFixed(6)} USDC`);
        console.log(`Raw / 10^15: ${(rawAmount / Math.pow(10, 15)).toFixed(6)} USDC`);
        console.log(`Raw / 10^18: ${(rawAmount / Math.pow(10, 18)).toFixed(6)} USDC`);
        
        // Check if exchange rate is involved
        if (usdcReserve.stats?.cTokenExchangeRate) {
          const exchangeRate = parseFloat(usdcReserve.stats.cTokenExchangeRate.toString());
          console.log(`\n--- WITH EXCHANGE RATE ---`);
          console.log(`Exchange rate: ${exchangeRate}`);
          console.log(`Raw * exchange rate / 10^6: ${(rawAmount * exchangeRate / Math.pow(10, 6)).toFixed(6)} USDC`);
          console.log(`Raw * exchange rate / 10^18: ${(rawAmount * exchangeRate / Math.pow(10, 18)).toFixed(6)} USDC`);
          console.log(`Raw * exchange rate / 10^24: ${(rawAmount * exchangeRate / Math.pow(10, 24)).toFixed(6)} USDC`);
        }
        
        console.log(`\n🎯 EXPECTED: 8,321.496 USDC`);
        console.log(`🎯 CURRENT CODE SHOWS: ${(rawAmount / Math.pow(10, 6)).toFixed(6)} USDC`);
      }
    });
    
    // Check pool totals for comparison
    console.log('\n=== POOL TOTALS (from save.finance) ===');
    console.log('Expected USDC deposits in pool: 4,306,536.898161490045063063743026');
    console.log('Expected TRUNK deposits in pool: 9,581,915.35575377377826236411752645');
    console.log('Expected TVL: $662,722.13');
    console.log('Expected total deposits: $4,969,056.90');
    console.log('Expected total borrows: $4,306,334.76');
    
    if (usdcReserve.stats) {
      const totalDepositsWads = parseFloat(usdcReserve.stats.totalDepositsWads.toString());
      const totalBorrowsWads = parseFloat(usdcReserve.stats.totalBorrowsWads.toString());
      const totalLiquidityWads = parseFloat(usdcReserve.stats.totalLiquidityWads.toString());
      
      console.log('\n=== ACTUAL POOL DATA ===');
      console.log(`Raw totalDepositsWads: ${totalDepositsWads}`);
      console.log(`Raw totalBorrowsWads: ${totalBorrowsWads}`);
      console.log(`Raw totalLiquidityWads: ${totalLiquidityWads}`);
      
      console.log('\n--- POOL CONVERSION ATTEMPTS ---');
      console.log(`Deposits / 10^6: ${(totalDepositsWads / Math.pow(10, 6)).toFixed(6)}`);
      console.log(`Deposits / 10^18: ${(totalDepositsWads / Math.pow(10, 18)).toFixed(6)}`);
      console.log(`Deposits / 10^24: ${(totalDepositsWads / Math.pow(10, 24)).toFixed(6)}`);
      
      console.log(`\nBorrows / 10^6: ${(totalBorrowsWads / Math.pow(10, 6)).toFixed(6)}`);
      console.log(`Borrows / 10^18: ${(totalBorrowsWads / Math.pow(10, 18)).toFixed(6)}`);
      console.log(`Borrows / 10^24: ${(totalBorrowsWads / Math.pow(10, 24)).toFixed(6)}`);
      
      console.log(`\nLiquidity / 10^6: ${(totalLiquidityWads / Math.pow(10, 6)).toFixed(6)}`);
      console.log(`Liquidity / 10^18: ${(totalLiquidityWads / Math.pow(10, 18)).toFixed(6)}`);
      console.log(`Liquidity / 10^24: ${(totalLiquidityWads / Math.pow(10, 24)).toFixed(6)}`);
      
      // Calculate available liquidity
      const availableLiquidity = totalDepositsWads - totalBorrowsWads;
      console.log(`\nAvailable liquidity raw: ${availableLiquidity}`);
      console.log(`Available / 10^6: ${(availableLiquidity / Math.pow(10, 6)).toFixed(6)}`);
      console.log(`Available / 10^18: ${(availableLiquidity / Math.pow(10, 18)).toFixed(6)}`);
      console.log(`Available / 10^24: ${(availableLiquidity / Math.pow(10, 24)).toFixed(6)}`);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

if (require.main === module) {
  debugPosition();
}

export { debugPosition };
