TRUNK-USDC Pool Monitor Project Summary
Project Overview
A TypeScript/Node.js monitoring script to automatically withdraw USDC from a TRUNK-USDC pool on Save.Finance (Solend protocol) when liquidity becomes available. The user has ~8,550 USDC stuck in a pool due to 100% utilization (all USDC borrowed out).

User Situation
Wallet Address: En9w4JmoYh894xy4qxx1JkWHsifPLEA8U3hDEovpvV32
Pool Address: 616Kxm68sCsFJTzKfqJYt1o2Na5qrujgfhrhUJsSPXHx (this is actually the market address for a permissionless pool)
Obligation Address: BpgFy4iymzUjLzWiP3fGVASnLh1wqcT7NWnoTcybMQwg
Deposits: 8,550.712936 USDC (originally 8,513, user added ~37.5 USDC for testing)
Problem: USDC pool has 100% utilization - all USDC has been borrowed, so withdrawal is impossible until borrowers repay
Health Factor: 85,126.78 (extremely healthy, no liquidation risk)
No Borrows: User has no outstanding loans
Technical Architecture
Key Files Structure
Configuration (.env)
PRIVATE_KEY=5W2e4XCGK6yVkCQ5Mn9T2DGUWRpvcyyZ2qqpToatqLdodBvR7UUwaGyhJiSdyzGK6hqwXavd8FBCh11AL5WW1bec
WALLET_ADDRESS=En9w4JmoYh894xy4qxx1JkWHsifPLEA8U3hDEovpvV32
POOL_ADDRESS=616Kxm68sCsFJTzKfqJYt1o2Na5qrujgfhrhUJsSPXHx
OBLIGATION_ADDRESS=BpgFy4iymzUjLzWiP3fGVASnLh1wqcT7NWnoTcybMQwg
RPC_ENDPOINT=https://api.mainnet-beta.
Critical Technical Discoveries
1. Pool Structure
NOT a standard Solend main market pool
Permissionless pool where 616Kxm68sCsFJTzKfqJYt1o2Na5qrujgfhrhUJsSPXHx is the market address
Contains two reserves:
TRUNK: 9mV4WUukVsva5wYcYW4veo34CNDiF44sh3Ji65JNdvh5 (reserve: 9SdGVn594S8fk8ZU3xcvZ1Cd4fjJgNxCN32upHhFZgJu)
USDC: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v (reserve: 8nXn5zEXFbAvXyeYgLLethMVfHxU4QgyG2GVrHmKojqR)
2. User's Actual Position
User deposited USDC, NOT TRUNK (initial confusion in development)
Deposit amount: 8,550,712,936 (raw) = 8,550.712936 USDC (6 decimals)
Found in obligation deposits array: Deposit 0: 8550712936 of EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
3. WAD Format Issue (CRITICAL BUG)
Current Problem: Liquidity calculation is incorrect due to decimal conversion issues.

Raw values from last debug:

Issue: WAD format uses 18 decimals, but the current calculation shows 2,000,000 USDC available when it should be ~2 USDC (user added 2 USDC for testing).

Expected: After user added 2 USDC, available liquidity should be ~2 USDC, not 2 million.

Current Status
Working Components ✅
Connection & Authentication: Successfully connects to Solana mainnet
Market Discovery: Correctly identifies permissionless market structure
Position Detection: Accurately finds user's USDC deposits
Real-time Monitoring: Account change detection working
Notification System: Discord/Telegram integration functional
Health Monitoring: Correctly calculates health factor (85,126.78)
Broken Components ❌
Liquidity Calculation: Shows 2M USDC instead of 2 USDC (WAD decimal issue)
Withdrawal Function: Multiple failed attempts with different errors:
"Obligation has no deposits" / "Obligation deposits are empty"
"Could not find asset in reserves"
"insufficient account keys for instruction"
"NotEnoughAccountKeys"
Last Error in Withdrawal
```
Transaction simulation failed: Error processing Instruction 3: insufficient account keys for instruction
Program log: Error: NotEnoughAccountKeys
```

Commands Available
'''
npm run setup                 # Interactive configuration
npm run test                  # Test connection & config
npm run test-monitor         # Test monitoring functionality
npm run test-withdrawal      # Test withdrawal (currently failing)
npm run find-position        # Find user position across markets
npm run dev status           # Show current pool status
npm run dev monitor          # Standard monitoring (1-min intervals)
npm run monitor-realtime     # Real-time monitoring (30s + account changes)
'''

Dependencies
'''
{
  "@solana/web3.js": "^1.95.4",
  "@solana/spl-token": "^0.4.8",
  "@solendprotocol/solend-sdk": "^0.6.16",
  "bn.js": "^5.2.1",
  "bs58": "^5.0.0",
  "dotenv": "^16.4.5",
  "node-cron": "^3.0.3",
  "axios": "^1.7.9"
}
'''

Immediate Next Steps Required
1. Fix Liquidity Calculation (HIGH PRIORITY)
The WAD decimal conversion in src/solend-client.ts lines 155-183 needs correction. Current debug shows wrong values.

2. Fix Withdrawal Function (HIGH PRIORITY)
The attemptWithdrawal method in src/solend-client.ts lines 262-320 fails with "NotEnoughAccountKeys". Likely issues:
* Permissionless market requires different account structure
* SolendAction.buildWithdrawTxns may not support custom markets
* May need direct transaction building instead of SDK

3. Test Scenarios
* User added 2 USDC to test liquidity detection
* Monitor should detect ~2 USDC available (not 2M)
* Should attempt withdrawal when liquidity appears

Key Technical Insights for Continuation
1. Market Initialization: Use SolendMarket.initialize(connection, 'production', poolAddress) where poolAddress is the market address
2. USDC Reserve: Always use USDC mint EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v for calculations
3. WAD Format: All *Wads values use 18 decimals, not token decimals (6 for USDC)
4. Obligation Fetching: market.fetchObligationByWallet(new PublicKey(walletAddress)) works correctly
5. Real-time Monitoring: Account change detection on 8nXn5zEXFbAvXyeYgLLethMVfHxU4QgyG2GVrHmKojqR (USDC reserve) works

User Preferences
* TypeScript and pure Node.js preferred
* Wants USDC withdrawal specifically (not TRUNK)
* Prefers frequent monitoring (1-minute or real-time)
* Wants immediate withdrawal when liquidity appears
* Minimum withdrawal threshold: 1 USDC
* The project is 90% complete but blocked on the liquidity calculation bug and withdrawal transaction building for permissionless markets.

